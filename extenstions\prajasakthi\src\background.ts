// Background script for Prajasakti Epaper Downloader

interface DownloadRequest {
  url: string;
  filename: string;
  cookies: string;
}

interface EpaperData {
  date: string;
  editions: Array<{
    id: string;
    name: string;
    url: string;
  }>;
}

// Listen for messages from popup
chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
  console.log('🔧 Background: Received message:', request);

  if (request.action === 'getCookies') {
    console.log('🍪 Background: Getting cookies for URL:', request.url);
    getCookiesForSite(request.url)
      .then(cookies => {
        console.log('🍪 Background: Successfully got cookies, length:', cookies.length);
        sendResponse({ success: true, cookies });
      })
      .catch(error => {
        console.error('🍪 Background: Failed to get cookies:', error);
        sendResponse({ success: false, error: error.message });
      });
    return true; // Keep message channel open for async response
  }

  if (request.action === 'downloadPDF') {
    downloadPDF(request.data)
      .then(result => sendResponse({ success: true, result }))
      .catch(error => sendResponse({ success: false, error: error.message }));
    return true;
  }

  if (request.action === 'fetchEpaperData') {
    fetchEpaperData(request.url, request.cookies)
      .then(data => sendResponse({ success: true, data }))
      .catch(error => sendResponse({ success: false, error: error.message }));
    return true;
  }
});

// Get cookies for the specified site
async function getCookiesForSite(url: string): Promise<string> {
  try {
    console.log('🍪 getCookiesForSite: Fetching cookies for:', url);

    // Method 1: Try Chrome cookies API
    const domains = [
      url, // Original URL
      'https://epaper.prajasakti.com/',
      'https://prajasakti.com/',
      'https://www.prajasakti.com/'
    ];

    let allCookies: chrome.cookies.Cookie[] = [];

    for (const domain of domains) {
      try {
        console.log('🍪 getCookiesForSite: Trying domain:', domain);
        const cookies = await chrome.cookies.getAll({ url: domain, partitionKey: {} });
        console.log('🍪 getCookiesForSite: Found', cookies.length, 'cookies for', domain);

        // Add cookies that we haven't already found
        for (const cookie of cookies) {
          if (!allCookies.find(c => c.name === cookie.name && c.domain === cookie.domain)) {
            allCookies.push(cookie);
          }
        }
      } catch (error) {
        console.log('🍪 getCookiesForSite: Failed to get cookies for', domain, error);
      }
    }

    // Method 1.5: Try domain-specific searches for parent domain
    try {
      console.log('🍪 getCookiesForSite: Trying domain-specific search for .prajasakti.com...');
      const domainCookies = await chrome.cookies.getAll({ domain: '.prajasakti.com',partitionKey: {} });
      console.log('🍪 getCookiesForSite: Found', domainCookies.length, 'cookies for .prajasakti.com domain');

      for (const cookie of domainCookies) {
        if (!allCookies.find(c => c.name === cookie.name && c.domain === cookie.domain)) {
          allCookies.push(cookie);
        }
      }
    } catch (error) {
      console.log('🍪 getCookiesForSite: Domain search failed:', error);
    }

    // Method 1.6: Try searching all cookies and filter for prajasakti
    try {
      console.log('🍪 getCookiesForSite: Trying all cookies search...');
      const allSiteCookies = await chrome.cookies.getAll({},partitionKey: {});
      const relevantCookies = allSiteCookies.filter(cookie =>
        cookie.domain.includes('prajasakti.com') ||
        cookie.domain.includes('.prajasakti.com')
      );
      console.log('🍪 getCookiesForSite: Found', relevantCookies.length, 'relevant cookies from all cookies');

      for (const cookie of relevantCookies) {
        if (!allCookies.find(c => c.name === cookie.name && c.domain === cookie.domain)) {
          allCookies.push(cookie);
        }
      }
    } catch (error) {
      console.log('🍪 getCookiesForSite: All cookies search failed:', error);
    }

    console.log('🍪 getCookiesForSite: Total unique cookies found:', allCookies.length);

    // Method 2: Always try content script injection to supplement Chrome API cookies
    console.log('🍪 getCookiesForSite: Trying content script to supplement Chrome API cookies...');
    try {
      const cookiesFromContent = await getCookiesFromContentScript();
      if (cookiesFromContent) {
        console.log('🍪 getCookiesForSite: Got cookies from content script:', cookiesFromContent.length, 'chars');

        // If we have Chrome API cookies, combine them with content script cookies
        if (allCookies.length > 0) {
          const chromeApiCookieString = allCookies.map(cookie => `${cookie.name}=${cookie.value}`).join('; ');
          const combinedCookies = chromeApiCookieString + '; ' + cookiesFromContent;
          console.log('🍪 getCookiesForSite: Combined Chrome API + content script cookies:', combinedCookies.length, 'chars');
          return combinedCookies;
        } else {
          // If no Chrome API cookies, just return content script cookies
          return cookiesFromContent;
        }
      }
    } catch (error) {
      console.log('🍪 getCookiesForSite: Content script method failed:', error);
    }

    console.log('🍪 getCookiesForSite: Cookie details:', allCookies.map(c => ({
      name: c.name,
      domain: c.domain,
      secure: c.secure,
      httpOnly: c.httpOnly
    })));

    const cookieString = allCookies.map(cookie => `${cookie.name}=${cookie.value}`).join('; ');
    console.log('🍪 getCookiesForSite: Final cookie string length:', cookieString.length);

    return cookieString;
  } catch (error) {
    console.error('🍪 getCookiesForSite: Error getting cookies:', error);
    throw error;
  }
}

// Get cookies from content script (fallback method)
async function getCookiesFromContentScript(): Promise<string> {
  try {
    // Get the active tab
    const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
    if (tabs.length === 0) {
      throw new Error('No active tab found');
    }

    const tab = tabs[0];
    if (!tab.id) {
      throw new Error('No tab ID found');
    }

    console.log('🍪 getCookiesFromContentScript: Injecting script into tab:', tab.url);

    // Inject script to get document.cookie
    const results = await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      func: () => {
        console.log('🍪 Content script: Getting document.cookie');
        const cookies = document.cookie;
        console.log('🍪 Content script: Found cookies:', cookies.length, 'chars');
        return cookies;
      }
    });

    if (results && results[0] && results[0].result) {
      return results[0].result;
    }

    throw new Error('No cookies returned from content script');
  } catch (error) {
    console.error('🍪 getCookiesFromContentScript: Error:', error);
    throw error;
  }
}

// Download PDF with cookies
async function downloadPDF(data: DownloadRequest): Promise<string> {
  try {
    const response = await fetch(data.url, {
      headers: {
        'Cookie': data.cookies,
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const blob = await response.blob();
    const url = URL.createObjectURL(blob);

    const downloadId = await chrome.downloads.download({
      url: url,
      filename: data.filename,
      saveAs: true
    });

    return `Download started with ID: ${downloadId}`;
  } catch (error) {
    console.error('Error downloading PDF:', error);
    throw error;
  }
}

// Fetch epaper data from the website
async function fetchEpaperData(url: string, cookies: string): Promise<EpaperData> {
  try {
    const response = await fetch(url, {
      headers: {
        'Cookie': cookies,
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const html = await response.text();
    return parseEpaperHTML(html, url);
  } catch (error) {
    console.error('Error fetching epaper data:', error);
    throw error;
  }
}

// Parse HTML to extract edition information and PDF links
function parseEpaperHTML(html: string, baseUrl: string): EpaperData {
  const parser = new DOMParser();
  const doc = parser.parseFromString(html, 'text/html');
  
  // Extract date from URL
  const urlParams = new URLSearchParams(new URL(baseUrl).search);
  const date = urlParams.get('date') || new Date().toISOString().split('T')[0];
  
  // Find edition links
  const editions: Array<{ id: string; name: string; url: string }> = [];
  const editionLinks = doc.querySelectorAll('a[onclick*="active_edition"]');
  
  editionLinks.forEach(link => {
    const onclick = link.getAttribute('onclick');
    const match = onclick?.match(/active_edition\("(\d+)"\)/);
    if (match) {
      const editionId = match[1];
      const editionName = link.textContent?.trim() || `Edition ${editionId}`;
      const editionUrl = `${new URL(baseUrl).origin}/view?date=${date}&edition=${editionId}&pg_no=1`;
      
      editions.push({
        id: editionId,
        name: editionName,
        url: editionUrl
      });
    }
  });

  return {
    date,
    editions: editions.filter((edition, index, self) => 
      index === self.findIndex(e => e.id === edition.id)
    )
  };
}

// Initialize extension
chrome.runtime.onInstalled.addListener(() => {
  console.log('Prajasakti Epaper Downloader installed');
});
